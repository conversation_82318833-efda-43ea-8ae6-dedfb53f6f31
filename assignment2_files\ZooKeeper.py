#declaration
# [x] <-- by marking a x in the box here, 
# I <PERSON><PERSON> (48738804) do affirm that this is my own work as outlined in the unit guide 
# and in previous assignments in this unit.

from Animalgroup import Animalgroup

def create_animal_groups(filename):
    groups = {}  # dictionary of animal groups

    if filename == "":
        # Create 3 groups: wombat, koala, kangaroo
        wombat_diet = {"grasses": 2, "leaves": 1, "shrubs": 1, "fungi": 1}
        koala_diet = {"eucalyptus leaves": 6}
        kangaroo_diet = {"grasses": 4, "leaves": 4, "fruits": 2}

        groups["wombat"] = Animalgroup("wombat", 8, wombat_diet)
        groups["koala"] = Animalgroup("koala", 8, koala_diet)
        groups["kangaroo"] = Animalgroup("kangaroo", 4, kangaroo_diet)
    else:
        # Read in the definition from the file for what groups to create
        with open(filename, "r") as file:
            for line in file:
                group_name, number, diet_str = line.strip().split(";")
                number = int(number)
                diet = {}
                for item in diet_str.split(","):
                    food, demand = item.split("*")
                    diet[food] = int(demand)
                groups[group_name] = Animalgroup(group_name, number, diet)

    return groups


def get_food_supply(filename):
    food_supply = {}  # dictionary of fresh food

    if filename == "":
        # Create 4 kinds of food with predefined amounts
        food_supply = {
            "fungi": 8,
            "grasses": 20,
            "leaves": 20,
            "fruits": 10
        }
    else:
        # Read in the definition from the file for what foods to create
        with open(filename, "r") as file:
            for line in file:
                food, amount = line.strip().split(";")
                food_supply[food] = int(amount)

    return food_supply


def find_compatible_groups(the_groups, food_supply):
    '''
    HD question
    given the list of animal groups and the food_supply, which groups could the food be served to? (According to the calculateServe function.)

    return a list of animal group names: these are the animal groups that are compatible with the food supply.  
    '''
    compatible_groups = []

    for group_name, animal_group in the_groups.items():
        compatible = True
        for food_type, food_amount in food_supply.items():
            if food_type in animal_group.diet_per_animal:
                # Check if the food supply can serve all animals in the group
                if animal_group.calculateServe(food_type, food_amount) < animal_group.number_of_animals:
                    compatible = False
                    break
        if compatible:
            compatible_groups.append(group_name)

    return compatible_groups


# Do not edit anything below this line
# If you make any changes here, you should return it to the original state before submitting
if __name__ == "__main__":
    filename = ""
    the_groups = create_animal_groups(filename)
    
    food_supply_filename = ""
    food_supply = get_food_supply(food_supply_filename)

    possible_list = []
    possible_list = find_compatible_groups(the_groups, food_supply)

    print(possible_list)  # prints the list of groups the food supply could be served to