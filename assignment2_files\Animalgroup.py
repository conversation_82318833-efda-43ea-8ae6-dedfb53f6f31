#declaration
# [x] <-- by marking a x in the box here, 
# I <PERSON><PERSON> (48738804) do affirm that this is my own work as outlined in the unit guide 
# and in previous assignments in this unit.

from Animal import Animal

class Animalgroup:
    def __init__(self, species, number, diet):
        '''
        Creates a dictionary of animals.
        if number = 3, species = "wombat", 
        then the dictionary will be a collection of Animal objects
        group_animals = {1: Animal(1,"wombat"), 2: Animal(2,"wombat"), 3: Animal(3,"wombat")}
        if number <=0 or not a valid integer, then
        set group_animals = {} # the empty dictionary

        diet is a dict of food types and demands for each of the animal in this group, e.g. {"grasses":2,"leaves":1,"fungi":1} 

        variables to define:
        '''
        # Initialize the variables
        self.species = species
        self.number_of_animals = number if isinstance(number, int) and number > 0 else 0
        self.diet_per_animal = diet
        self.group_animals = {}

        # Populate the group_animals dictionary if the number is valid
        if self.number_of_animals > 0:
            for i in range(1, self.number_of_animals + 1):
                self.group_animals[i] = Animal(i, species)

    def __str__(self):
        '''
        This needs to return a string using the format below.
        Note, you can print an animal using the Animal class printer.
        Multi-line string format: (e.g. for a 3-wombats group)
        
        Species: wombat
        Number: 3
        -- Wombat: 1
        -- Wombat: 2
        -- Wombat: 3
        Diet: {"grasses":2,"leaves":1,"shrubs":1,"fungi":1} 
        '''
        # Build the string representation
        print_string = f"Species: {self.species}\n"
        print_string += f"Number: {self.number_of_animals}\n"
        for key, animal in self.group_animals.items():
            print_string += f"-- {self.species.capitalize()}: {key}\n"
        print_string += f"Diet: {self.diet_per_animal}"
        return print_string
    
    def calculateServe(self, food_type, food_amount):
        '''
        Returns how many animals in this group can be served the given food_amount servings of food_type. The result should be an integer. The result cannot be larger than self.number_of_animals.
        Note: if the food_amount servings of food_type are insufficient for even a single animal in this group, return 0.

        Examples:
        if food_type = "fungi" and food_amount = 3, according to the diet, then
        the 3 servings of fungi can be served to 3 wombats.
        In that case, the function would return 3.

        if food_type = "grasses" and food_amount = 3, according to the diet, then
        one wombat in this group can be satisfied, but there is not enough for 2. In that case, the function would return 0.
        '''
        # Check if the food type exists in the diet
        if food_type not in self.diet_per_animal:
            return 0

        # Calculate how many animals can be served
        required_per_animal = self.diet_per_animal[food_type]
        if required_per_animal <= 0:
            return 0

        max_animals_served = food_amount // required_per_animal
        return min(max_animals_served, self.number_of_animals)